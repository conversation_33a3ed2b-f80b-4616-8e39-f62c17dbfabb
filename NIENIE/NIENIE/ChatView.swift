import SwiftUI

// 消息模型
struct Message: Identifiable, Codable, Equatable {
    let message_id: Int
    let conversation_id: String
    let sender_id: Int
    let receiver_id: Int
    let content: String
    let status: Int
    let created_at: Date
    var is_self: Bool

    var id: Int { message_id }

    static func == (lhs: Message, rhs: Message) -> Bool {
        return lhs.message_id == rhs.message_id
    }
}

// 聊天视图的ViewModel
class ChatViewModel: ObservableObject {
    @Published var displayedMessages: [Message] = [] // 当前显示的消息（内存中的消息）
    @Published var inputText = ""
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var canLoadMoreHistory = false // 是否还能加载更多历史消息
    @Published var isLoadingHistory = false // 是否正在加载历史消息
    @Published var shouldScrollToBottom = false // 是否应该滚动到底部
    @Published var unreadMessageCount = 0 // 未读消息数量
    @Published var isViewingHistory = false // 是否正在查看历史消息

    private let conversationId: String
    private let userId: Int
    private let otherUserId: Int
    private var isRequestInProgress = false
    private let messagesPerPage = 30 // 每页显示的消息数量
    private let cacheManager = MessageCacheManager.shared
    private var lastVisibleMessageId: Int? // 用户最后查看的消息ID（标记消息）
    
    init(conversationId: String, userId: Int, otherUserId: Int) {
        self.conversationId = conversationId
        self.userId = userId
        self.otherUserId = otherUserId
        // 只加载本地缓存的最新消息，不重复调用后端
        loadInitialMessages()
    }
    
    // 加载用户信息
    func loadUserInfo(userId: Int, completion: @escaping (String) -> Void) {
        guard let url = URL(string: "https://sorealhuman.com:8888/users/\(userId)/info") else {
            return
        }
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            guard let data = data, error == nil else {
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool, success,
                   let userData = json["user"] as? [String: Any],
                   let avatar = userData["avatar"] as? String {
                    
                    DispatchQueue.main.async {
                        completion(avatar)
                    }
                }
            } catch {
                print("解析用户信息失败: \(error)")
            }
        }.resume()
    }
    
    func loadMessages() {
        // 防止重复请求
        if isRequestInProgress {
            return
        }

        isLoading = true
        isRequestInProgress = true
        errorMessage = nil

        // 获取当前会话的最后一条消息ID
        let lastMessageId = LastMessageIDManager.shared.getLastMessageID(for: conversationId)

        guard let url = URL(string: "https://sorealhuman.com:8888/messages/conversation/\(conversationId)/user/\(userId)?last_message_id=\(lastMessageId)") else {
            isLoading = false
            isRequestInProgress = false
            errorMessage = "无效的URL"
            return
        }
        
        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                self?.isRequestInProgress = false
                
                if let error = error {
                    self?.errorMessage = "加载失败: \(error.localizedDescription)"
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    self?.errorMessage = "无效的响应"
                    return
                }
                
                if httpResponse.statusCode == 404 {
                    // 确保消息列表为空
                    self?.displayedMessages = []
                    self?.cacheManager.clearCache(for: self?.conversationId ?? "")
                    return
                }
                
                if httpResponse.statusCode != 200 {
                    self?.errorMessage = "服务器错误: \(httpResponse.statusCode)"
                    return
                }
                
                guard let data = data else {
                    self?.errorMessage = "没有数据"
                    return
                }
                
                do {
                    // 先尝试解析为JSON，检查返回格式
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        // 检查是否有success字段
                        if let success = json["success"] as? Bool, success {
                            // 检查是否有data字段
                            if let responseData = json["data"] as? [String: Any] {
                                // 尝试获取messages数组
                                if let messagesArray = responseData["messages"] as? [[String: Any]] {
                                    // 有消息数据，解析它们
                                    var parsedMessages: [Message] = []

                                    for messageData in messagesArray {
                                        if let messageId = messageData["message_id"] as? Int,
                                           let senderId = messageData["sender_id"] as? Int,
                                           let receiverId = messageData["receiver_id"] as? Int {

                                            // content 可能是字符串或数字，需要转换为字符串
                                            let content: String
                                            if let contentString = messageData["content"] as? String {
                                                content = contentString
                                            } else if let contentNumber = messageData["content"] as? Int {
                                                content = String(contentNumber)
                                            } else if let contentNumber = messageData["content"] as? Double {
                                                content = String(contentNumber)
                                            } else {
                                                continue
                                            }

                                            // status 字段是可选的，如果没有则默认为1
                                            let status = messageData["status"] as? Int ?? 1


                                            // 处理时间字符串
                                            var createdAt = Date()
                                            if let timeString = messageData["created_at"] as? String {
                                                let formatter = ISO8601DateFormatter()
                                                createdAt = formatter.date(from: timeString) ?? Date()
                                            }

                                            let conversationId = messageData["conversation_id"] as? String ?? self?.conversationId ?? ""
                                            let isSelf = messageData["is_self"] as? Bool ?? (senderId == self?.userId)

                                            let message = Message(
                                                message_id: messageId,
                                                conversation_id: conversationId,
                                                sender_id: senderId,
                                                receiver_id: receiverId,
                                                content: content,
                                                status: status,
                                                created_at: createdAt,
                                                is_self: isSelf
                                            )

                                            parsedMessages.append(message)
                                        } else {
                                            print("消息解析失败，缺少必要字段: \(messageData)")
                                        }
                                    }

                                    // 将新消息添加到现有消息列表中
                                    self?.addNewMessages(parsedMessages)
                                    return
                                } else if let emptyArray = responseData["messages"] as? [Any], emptyArray.isEmpty {
                                    // 空数组，没有新消息
                                    print("没有新消息")
                                    return
                                }
                            }
                        } else {
                            // 请求不成功
                            if let message = json["message"] as? String {
                                self?.errorMessage = message
                            } else {
                                self?.errorMessage = "获取消息失败"
                            }
                            return
                        }
                    }
                    
                    // 如果上面的解析失败，尝试使用标准解码器
                    let decoder = JSONDecoder()
                    decoder.dateDecodingStrategy = .iso8601
                    
                    let response = try decoder.decode(APIResponse<[Message]>.self, from: data)
                    if response.success {
                        let messagesData = response.data ?? []
                        let processedMessages = messagesData.map { message in
                            var msg = message
                            msg.is_self = message.sender_id == self?.userId
                            return msg
                        }
                        self?.addNewMessages(processedMessages)
                    } else {
                        self?.errorMessage = response.message ?? "未知错误"
                    }
                } catch {
                    print("解析消息失败: \(error)")
                    // 解析失败时不需要特殊处理，显示列表保持当前状态
                }
            }
        }.resume()
    }

    /// 添加新消息到缓存和显示列表中
    private func addNewMessages(_ newMessages: [Message]) {
        guard !newMessages.isEmpty else {
            return
        }

        // 过滤掉已存在的消息（避免重复）
        let existingDisplayedIds = Set(displayedMessages.map { $0.message_id })
        let uniqueNewMessages = newMessages.filter { !existingDisplayedIds.contains($0.message_id) }

        if !uniqueNewMessages.isEmpty {
            // 添加新消息到缓存
            cacheManager.addMessages(uniqueNewMessages, for: conversationId)

            // 更新最后消息ID
            LastMessageIDManager.shared.updateLastMessageIDFromMessages(uniqueNewMessages, for: conversationId)

            // 添加新消息到显示列表
            displayedMessages.append(contentsOf: uniqueNewMessages)
            displayedMessages.sort { $0.created_at < $1.created_at }

            // 保持显示列表在合理大小内（最多显示最近的消息）
            if displayedMessages.count > messagesPerPage * 2 {
                displayedMessages = Array(displayedMessages.suffix(messagesPerPage * 2))
            }

            // 检查是否还有更多历史消息
            updateCanLoadMoreHistory()

            // 如果用户正在查看历史消息，不自动滚动，而是增加未读消息计数
            if isViewingHistory {
                unreadMessageCount += uniqueNewMessages.count
                shouldScrollToBottom = false
            } else {
                shouldScrollToBottom = true
                // 更新标记消息为最新消息
                if let lastMessage = displayedMessages.last {
                    lastVisibleMessageId = lastMessage.message_id
                }
            }
        }
    }

    /// 更新是否可以加载更多历史消息的状态
    private func updateCanLoadMoreHistory() {
        if let firstMessage = displayedMessages.first {
            canLoadMoreHistory = cacheManager.hasMoreHistory(for: conversationId, beforeMessage: firstMessage)
        } else {
            canLoadMoreHistory = cacheManager.getMessageCount(for: conversationId) > 0
        }
    }

    /// 从本地缓存加载更多历史消息到显示列表
    func loadMoreHistoryMessages() {
        guard canLoadMoreHistory && !isLoadingHistory else { return }
        guard let firstMessage = displayedMessages.first else { return }

        isLoadingHistory = true

        // 从缓存中加载更多历史消息
        let historyMessages = cacheManager.loadHistoryMessages(
            for: conversationId,
            beforeMessage: firstMessage,
            pageSize: messagesPerPage
        )

        if !historyMessages.isEmpty {
            // 将历史消息添加到显示列表的开头
            displayedMessages = historyMessages + displayedMessages
            shouldScrollToBottom = false // 加载历史消息时不滚动

            // 更新是否还能加载更多的状态
            updateCanLoadMoreHistory()

            print("从缓存加载了 \(historyMessages.count) 条历史消息，当前显示 \(displayedMessages.count) 条消息")
        } else {
            canLoadMoreHistory = false
        }

        isLoadingHistory = false
    }

    /// 加载初始消息（最新的一页消息）
    func loadInitialMessages() {
        // 从缓存加载最新的一页消息
        let latestMessages = cacheManager.loadLatestMessages(for: conversationId, pageSize: messagesPerPage)

        if !latestMessages.isEmpty {
            displayedMessages = latestMessages

            // 更新最后消息ID
            LastMessageIDManager.shared.updateLastMessageIDFromMessages(latestMessages, for: conversationId)

            // 设置初始标记消息为最后一条消息
            lastVisibleMessageId = latestMessages.last?.message_id

            print("从缓存加载了 \(latestMessages.count) 条最新消息")
        }

        // 更新是否可以加载更多历史消息
        updateCanLoadMoreHistory()

        shouldScrollToBottom = false // 初始加载时不滚动
        isViewingHistory = false // 初始状态不是查看历史消息
        unreadMessageCount = 0 // 初始未读消息数为0
    }

    /// 检查用户是否在查看历史消息
    func checkIfViewingHistory(visibleMessageIds: Set<Int>) {
        guard let lastVisibleId = lastVisibleMessageId else { return }

        // 如果标记的消息不在可见范围内，说明用户在查看历史消息
        let wasViewingHistory = isViewingHistory
        isViewingHistory = !visibleMessageIds.contains(lastVisibleId)

        // 如果从查看历史消息状态变为不查看，清零未读消息计数
        if wasViewingHistory && !isViewingHistory {
            unreadMessageCount = 0
            // 更新标记消息为当前最新的消息
            if let lastMessage = displayedMessages.last {
                lastVisibleMessageId = lastMessage.message_id
            }
        }
    }

    /// 用户滚动到底部时调用
    func userScrolledToBottom() {
        if isViewingHistory {
            isViewingHistory = false
            unreadMessageCount = 0
            // 更新标记消息为当前最新的消息
            if let lastMessage = displayedMessages.last {
                lastVisibleMessageId = lastMessage.message_id
            }
        }
    }

    // 注意：saveMessagesToCache 方法已被 MessageCacheManager 替代
    // 现在通过 cacheManager.addMessages() 和 cacheManager.saveMessages() 来管理缓存
    
    func sendMessage() {
        let content = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        if content.isEmpty {
            return
        }
        
        // 清空输入框
        inputText = ""
        
        guard let url = URL(string: "https://sorealhuman.com:8888/messages/send") else {
            errorMessage = "无效的URL"
            return
        }
        
        let parameters: [String: Any] = [
            "sender_id": userId,
            "receiver_id": otherUserId,
            "content": content
        ]
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        } catch {
            errorMessage = "参数编码失败"
            return
        }
        
        // 添加临时消息到UI
        let tempMessage = Message(
            message_id: -1, // 临时ID
            conversation_id: conversationId,
            sender_id: userId,
            receiver_id: otherUserId,
            content: content,
            status: 1,
            created_at: Date(),
            is_self: true
        )

        // 添加到显示列表
        displayedMessages.append(tempMessage)
        shouldScrollToBottom = true
        // 用户主动发送消息，不是查看历史消息状态
        isViewingHistory = false
        unreadMessageCount = 0
        
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.errorMessage = "发送失败: \(error.localizedDescription)"
                    // 移除临时消息
                    self?.displayedMessages.removeAll { $0.message_id == -1 }
                    return
                }
                
                guard let data = data else {
                    self?.errorMessage = "没有数据"
                    // 移除临时消息
                    self?.displayedMessages.removeAll { $0.message_id == -1 }
                    return
                }
                
                do {
                    // 先尝试解析为JSON
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        if let success = json["success"] as? Bool, success {
                            if let responseData = json["data"] as? [String: Any],
                               let messageId = responseData["message_id"] as? Int,
                               let conversationId = responseData["conversation_id"] as? String {
                                
                                // 创建正式消息
                                var createdAt = Date()
                                if let timeString = responseData["created_at"] as? String {
                                    let formatter = ISO8601DateFormatter()
                                    createdAt = formatter.date(from: timeString) ?? Date()
                                }
                                
                                let newMessage = Message(
                                    message_id: messageId,
                                    conversation_id: conversationId,
                                    sender_id: self?.userId ?? 0,
                                    receiver_id: self?.otherUserId ?? 0,
                                    content: content,
                                    status: 1,
                                    created_at: createdAt,
                                    is_self: true
                                )
                                
                                // 替换临时消息
                                if let index = self?.displayedMessages.firstIndex(where: { $0.message_id == -1 }) {
                                    self?.displayedMessages[index] = newMessage
                                } else {
                                    self?.displayedMessages.append(newMessage)
                                }

                                // 保存到缓存
                                self?.cacheManager.addMessages([newMessage], for: self?.conversationId ?? "")

                                // 更新最后消息ID
                                LastMessageIDManager.shared.updateLastMessageID(newMessage.message_id, for: self?.conversationId ?? "")

                                // 发送通知给会话列表页面，通知有新消息（用户主动发送）
                                NotificationCenter.default.post(
                                    name: NSNotification.Name("NewMessageReceived"),
                                    object: nil,
                                    userInfo: [
                                        "conversation_id": conversationId,
                                        "message_id": messageId,
                                        "sender_id": self?.userId ?? 0,
                                        "receiver_id": self?.otherUserId ?? 0,
                                        "content": content,
                                        "status": 1,
                                        "created_at": createdAt.ISO8601Format(),
                                        "is_self": true
                                    ]
                                )

                                // 触发滚动到底部
                                self?.shouldScrollToBottom = true
                                // 更新标记消息为新发送的消息
                                self?.lastVisibleMessageId = messageId
                                return
                            }
                        } else {
                            // 请求不成功
                            if let message = json["message"] as? String {
                                self?.errorMessage = message
                            } else {
                                self?.errorMessage = "发送消息失败"
                            }
                            // 移除临时消息
                            self?.displayedMessages.removeAll { $0.message_id == -1 }
                            return
                        }
                    }
                    
                    // 如果上面的解析失败，尝试使用标准解码器
                    let decoder = JSONDecoder()
                    decoder.dateDecodingStrategy = .iso8601
                    
                    let response = try decoder.decode(APIResponse<MessageResponse>.self, from: data)
                    if response.success, let responseData = response.data {
                        // 添加新消息到列表
                        let newMessage = Message(
                            message_id: responseData.message_id,
                            conversation_id: responseData.conversation_id,
                            sender_id: self?.userId ?? 0,
                            receiver_id: self?.otherUserId ?? 0,
                            content: content,
                            status: 1,
                            created_at: responseData.created_at,
                            is_self: true
                        )
                        
                        // 替换临时消息
                        if let index = self?.displayedMessages.firstIndex(where: { $0.message_id == -1 }) {
                            self?.displayedMessages[index] = newMessage
                        } else {
                            self?.displayedMessages.append(newMessage)
                        }

                        // 保存到缓存
                        self?.cacheManager.addMessages([newMessage], for: self?.conversationId ?? "")

                        // 更新最后消息ID
                        LastMessageIDManager.shared.updateLastMessageID(newMessage.message_id, for: self?.conversationId ?? "")

                        // 触发滚动到底部
                        self?.shouldScrollToBottom = true
                    } else {
                        self?.errorMessage = response.message ?? "未知错误"
                        // 移除临时消息
                        self?.displayedMessages.removeAll { $0.message_id == -1 }
                    }
                } catch {
                    print("解析失败: \(error)")
                    self?.errorMessage = "解析失败"
                    // 移除临时消息
                    self?.displayedMessages.removeAll { $0.message_id == -1 }
                }
            }
        }.resume()
    }
    
    func deleteMessage(_ message: Message) {
        // 从显示列表中删除消息
        displayedMessages.removeAll { $0.message_id == message.message_id }

        // 从缓存中删除消息
        cacheManager.deleteMessage(message.message_id, for: conversationId)

        // 更新是否可以加载更多历史消息
        updateCanLoadMoreHistory()
    }

    func clearChatHistory() {
        // 清空显示的消息列表
        displayedMessages = []

        // 清空缓存
        cacheManager.clearCache(for: conversationId)

        // 更新状态
        canLoadMoreHistory = false

        // 注意：不清除最后消息ID，保持会话的连续性

        // 发送通知，让会话列表页面刷新
        NotificationCenter.default.post(
            name: NSNotification.Name("ConversationHistoryCleared"),
            object: nil,
            userInfo: ["conversation_id": conversationId]
        )
    }

    func clearAllMessages() {
        // 清空内存中的消息数据（用于会话被删除时）
        displayedMessages = []
        canLoadMoreHistory = false

        // 不清除任何ID记录，保留最后消息ID和已读消息ID
        // 这样可以确保：
        // 1. 未读消息计算的连续性
        // 2. 从用户头像进入聊天时不会获取全部历史记录
    }

    func handleNewMessage(_ messageData: [String: Any]) {
        // 从WebSocket消息数据创建Message对象
        guard let messageId = messageData["message_id"] as? Int,
              let conversationId = messageData["conversation_id"] as? String,
              let senderId = messageData["sender_id"] as? Int,
              let receiverId = messageData["receiver_id"] as? Int,
              let content = messageData["content"] as? String,
              let status = messageData["status"] as? Int,
              let createdAtString = messageData["created_at"] as? String,
              let isSelf = messageData["is_self"] as? Bool else {
            return
        }

        // 解析时间
        let formatter = ISO8601DateFormatter()
        let createdAt = formatter.date(from: createdAtString) ?? Date()

        // 创建新消息
        let newMessage = Message(
            message_id: messageId,
            conversation_id: conversationId,
            sender_id: senderId,
            receiver_id: receiverId,
            content: content,
            status: status,
            created_at: createdAt,
            is_self: isSelf
        )

        // 检查消息是否已存在（避免重复）
        if !displayedMessages.contains(where: { $0.message_id == messageId }) {
            displayedMessages.append(newMessage)
            displayedMessages.sort { $0.created_at < $1.created_at }

            // 保存到缓存
            cacheManager.addMessages([newMessage], for: conversationId)

            // 更新最后消息ID
            LastMessageIDManager.shared.updateLastMessageID(messageId, for: conversationId)

            // 如果用户正在查看历史消息，不自动滚动，而是增加未读消息计数
            if isViewingHistory {
                unreadMessageCount += 1
                shouldScrollToBottom = false
            } else {
                shouldScrollToBottom = true
                // 更新标记消息为最新消息
                lastVisibleMessageId = messageId
            }
        }
    }
}

struct ChatView: View {
    @StateObject private var viewModel: ChatViewModel
    @State private var showingDeleteAlert = false
    @State private var messageToDelete: Message?
    @State private var keyboardHeight: CGFloat = 0
    @State private var showSettingsSheet = false
    @State private var textHeight: CGFloat = 36 // 初始文本框高度
    @State private var dragOffset: CGFloat = 0 // 右滑手势的偏移量
    @State private var showUserProfile = false // 显示用户主页的状态
    @State private var visibleMessageIds: Set<Int> = [] // 当前可见的消息ID集合
    @Environment(\.dismiss) var dismiss

    let conversationId: String
    let otherUserId: Int
    let otherUsername: String
    let userId: Int
    @State private var otherUserAvatar: String = ""
    
    init(conversationId: String, otherUserId: Int, otherUsername: String, userId: Int, otherUserAvatar: String = "") {
        self.conversationId = conversationId
        self.otherUserId = otherUserId
        self.otherUsername = otherUsername
        self.userId = userId
        self._otherUserAvatar = State(initialValue: otherUserAvatar)
        _viewModel = StateObject(wrappedValue: ChatViewModel(conversationId: conversationId, userId: userId, otherUserId: otherUserId))
    }
    
    var body: some View {
        ZStack {
            Color.white.edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    // 点击非键盘区域关闭键盘
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }

            VStack(spacing: 0) {
                // 聊天内容区域
                if viewModel.isLoading && viewModel.displayedMessages.isEmpty {
                    Spacer()
                    ProgressView()
                    Spacer()
                } else if let errorMessage = viewModel.errorMessage, viewModel.displayedMessages.isEmpty {
                    Spacer()
                    VStack {
                        Text(errorMessage)
                            .foregroundColor(.gray)
                        
                        Button("重试") {
                            viewModel.loadMessages()
                        }
                        .padding()
                        .foregroundColor(.white)
                        .background(Color.blue)
                        .cornerRadius(8)
                        .padding(.top)
                    }
                    Spacer()
                } else {
                    ZStack {
                        ScrollViewReader { scrollView in
                            ScrollView {
                                LazyVStack(spacing: 10) {
                                    // 下拉加载更多历史消息的指示器
                                    if viewModel.canLoadMoreHistory {
                                        HStack {
                                            Spacer()
                                            if viewModel.isLoadingHistory {
                                                ProgressView()
                                                    .scaleEffect(0.8)
                                                Text("加载中...")
                                                    .font(.caption)
                                                    .foregroundColor(.gray)
                                            } else {
                                                Text("下拉加载更多历史消息")
                                                    .font(.caption)
                                                    .foregroundColor(.gray)
                                            }
                                            Spacer()
                                        }
                                        .padding(.vertical, 8)

                                    }

                                    ForEach(viewModel.displayedMessages) { message in
                                        MessageBubble(
                                            message: message,
                                            otherUserAvatar: otherUserAvatar,
                                            currentUserAvatar: UserState.shared.avatar,
                                            onAvatarTap: {
                                                presentUserProfile()
                                            }
                                        )
                                        .id(message.id)
                                        .onAppear {
                                            // 记录可见的消息ID
                                            visibleMessageIds.insert(message.message_id)
                                            // 检查是否在查看历史消息
                                            viewModel.checkIfViewingHistory(visibleMessageIds: visibleMessageIds)
                                        }
                                        .onDisappear {
                                            // 移除不可见的消息ID
                                            visibleMessageIds.remove(message.message_id)
                                            // 检查是否在查看历史消息
                                            viewModel.checkIfViewingHistory(visibleMessageIds: visibleMessageIds)
                                        }
                                        .onLongPressGesture {
                                            messageToDelete = message
                                            showingDeleteAlert = true
                                        }
                                    }
                                }
                                .padding(.horizontal)
                                .padding(.top, 10)
                            }
                            .refreshable {
                                // 下拉刷新时加载更多历史消息
                                if viewModel.canLoadMoreHistory {
                                    viewModel.loadMoreHistoryMessages()
                                }
                            }
                            .onTapGesture {
                                // 点击聊天区域关闭键盘
                                UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                            }
                            .onChange(of: viewModel.displayedMessages) { oldMessages, newMessages in
                                // 只有在应该滚动到底部时才滚动
                                if viewModel.shouldScrollToBottom, let lastMessage = newMessages.last {
                                    withAnimation {
                                        scrollView.scrollTo(lastMessage.id, anchor: .bottom)
                                    }
                                }
                            }
                            .onAppear {
                                if let lastMessage = viewModel.displayedMessages.last {
                                    scrollView.scrollTo(lastMessage.id, anchor: .bottom)
                                }
                            }
                            .simultaneousGesture(
                                DragGesture()
                                    .onEnded { value in
                                        // 检测用户是否滚动到底部
                                        if value.translation.y < -50 { // 向上滑动超过50点
                                            // 检查是否滚动到底部
                                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                                if let lastMessage = viewModel.displayedMessages.last,
                                                   visibleMessageIds.contains(lastMessage.message_id) {
                                                    viewModel.userScrolledToBottom()
                                                }
                                            }
                                        }
                                    }
                            )
                        }

                        // 未读消息提示
                        if viewModel.unreadMessageCount > 0 {
                            VStack {
                                Spacer()
                                HStack {
                                    Spacer()
                                    Button(action: {
                                        // 点击提示滚动到底部
                                        if let lastMessage = viewModel.displayedMessages.last {
                                            withAnimation {
                                                scrollView.scrollTo(lastMessage.id, anchor: .bottom)
                                            }
                                        }
                                        viewModel.userScrolledToBottom()
                                    }) {
                                        HStack(spacing: 4) {
                                            Text("\(viewModel.unreadMessageCount)")
                                                .font(.caption)
                                                .fontWeight(.medium)
                                                .foregroundColor(.white)
                                            Text("条新消息")
                                                .font(.caption)
                                                .foregroundColor(.white)
                                        }
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(Color.blue)
                                        .cornerRadius(15)
                                        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                                    }
                                    Spacer()
                                }
                                .padding(.bottom, 80) // 在输入框上方一点的位置
                            }
                        }
                    }
                }
                
                // 底部输入框
                VStack(spacing: 0) {
                    Divider()

                    HStack(alignment: .bottom, spacing: 8) {
                        // 多行文本输入框
                        ZStack(alignment: .topLeading) {
                            // 占位符
                            if viewModel.inputText.isEmpty {
                                Text("发送消息")
                                    .foregroundColor(.gray.opacity(0.8))
                                    .padding(.leading, 12)
                                    .padding(.top, 12)
                                    .allowsHitTesting(false)
                            }

                            // 使用自定义TextEditor
                            ChatTextEditor(
                                text: $viewModel.inputText,
                                onSubmit: {
                                    viewModel.sendMessage()
                                },
                                onTextChange: {
                                    updateTextHeight()
                                }
                            )
                            .frame(minHeight: textHeight, maxHeight: min(textHeight, 108)) // 最大3行高度
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color(UIColor.systemGray6))
                            .cornerRadius(16)
                        }

                        // 发送按钮
                        Button(action: {
                            // 发送消息
                            viewModel.sendMessage()
                        }) {
                            Text("发送")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(viewModel.inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? .gray : .white)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(viewModel.inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? Color.gray.opacity(0.3) : Color.blue)
                                .cornerRadius(16)
                        }
                        .disabled(viewModel.inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                        .simultaneousGesture(
                            LongPressGesture(minimumDuration: 0.5)
                                .onEnded { _ in
                                    // 长按换行
                                    viewModel.inputText += "\n"
                                    updateTextHeight()
                                }
                        )
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                }
            }
            .offset(x: dragOffset)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        // 只允许向右滑动
                        if value.translation.width > 0 {
                            dragOffset = value.translation.width
                        }
                    }
                    .onEnded { value in
                        // 如果滑动距离超过50点，则返回上一页
                        if value.translation.width > 50 {
                            dismiss()
                        } else {
                            // 否则回弹到原位置
                            withAnimation(.spring()) {
                                dragOffset = 0
                            }
                        }
                    }
            )
            .navigationTitle(otherUsername)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .navigationBarItems(
                leading: HStack(spacing: 8) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.black)
                    }
                    
                    // 显示对方头像
                    if !otherUserAvatar.isEmpty {
                        Button(action: {
                            presentUserProfile()
                        }) {
                            if otherUserAvatar.contains("http") {
                                // 远程头像
                                AsyncImage(url: URL(string: otherUserAvatar)) { phase in
                                    if let image = phase.image {
                                        image
                                            .resizable()
                                            .scaledToFill()
                                    } else {
                                        Circle()
                                            .fill(Color.gray.opacity(0.3))
                                    }
                                }
                                .frame(width: 30, height: 30)
                                .clipShape(Circle())
                            } else {
                                // 本地头像
                                Image(otherUserAvatar)
                                    .resizable()
                                    .scaledToFill()
                                    .frame(width: 30, height: 30)
                                    .clipShape(Circle())
                            }
                        }
                    }
                },
                trailing: Button(action: {
                    showSettingsSheet = true
                }) {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.black)
                        .padding(.horizontal)
                }
            )
            .alert(isPresented: $showingDeleteAlert) {
                Alert(
                    title: Text("删除消息"),
                    message: Text("确定要删除这条消息吗？"),
                    primaryButton: .destructive(Text("删除")) {
                        if let message = messageToDelete {
                            viewModel.deleteMessage(message)
                        }
                    },
                    secondaryButton: .cancel(Text("取消"))
                )
            }
            .sheet(isPresented: $showSettingsSheet) {
                ChatSettingsView(
                    conversationId: conversationId,
                    userId: userId,
                    otherUserId: otherUserId,
                    otherUsername: otherUsername,
                    otherUserAvatar: otherUserAvatar,
                    currentUserAvatar: UserState.shared.avatar,
                    onClearHistory: {
                        viewModel.clearChatHistory()
                        showSettingsSheet = false
                    }
                )
            }
        }
        .onAppear {
            // 设置当前页面状态并通过WebSocket更新
            UserState.shared.updatePageStatus(page: "ChatView", conversationId: conversationId)

            // 进入页面时获取增量消息（从后端同步最新消息）
            viewModel.loadMessages()

            // 注册通知观察者，监听新消息
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("NewMessageReceived"),
                object: nil,
                queue: .main
            ) { notification in
                if let userInfo = notification.userInfo,
                   let receivedConversationId = userInfo["conversation_id"] as? String,
                   receivedConversationId == conversationId {
                    // 收到当前会话的新消息，直接添加到UI而不重新加载
                    // 将 [AnyHashable: Any] 转换为 [String: Any]
                    let messageData = userInfo.compactMapValues { $0 }.reduce(into: [String: Any]()) { result, element in
                        if let key = element.key as? String {
                            result[key] = element.value
                        }
                    }
                    viewModel.handleNewMessage(messageData)
                }
            }

            // 注册通知观察者，监听从后台返回的刷新请求
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("RefreshChatAfterBackground"),
                object: nil,
                queue: .main
            ) { notification in
                if let userInfo = notification.userInfo,
                   let receivedConversationId = userInfo["conversation_id"] as? String,
                   receivedConversationId == conversationId {
                    // 重新加载消息以获取可能错过的新消息
                    viewModel.loadMessages()
                }
            }

            // 注册通知观察者，监听会话缓存清除
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("ConversationCacheCleared"),
                object: nil,
                queue: .main
            ) { notification in
                if let userInfo = notification.userInfo,
                   let clearedConversationId = userInfo["conversation_id"] as? String,
                   clearedConversationId == conversationId {
                    // 清空当前的消息数据
                    viewModel.clearAllMessages()
                }
            }

            // 如果没有头像信息，尝试从API获取
            if otherUserAvatar.isEmpty {
                viewModel.loadUserInfo(userId: otherUserId) { avatar in
                    if !avatar.isEmpty {
                        otherUserAvatar = avatar
                    }
                }
            }
        }
        .onDisappear {
            UserState.shared.updatePageStatus(page: "ConversationListView", conversationId: "")

            // 更新已读消息ID为当前会话的最后一条消息ID
            let lastMessageId = LastMessageIDManager.shared.getLastMessageID(for: conversationId)
            if lastMessageId > 0 {
                LastMessageIDManager.shared.setReadMessageID(lastMessageId, for: conversationId)
            }

            // 通知会话列表页面刷新当前会话状态（清零未读数）
            NotificationCenter.default.post(
                name: NSNotification.Name("ChatViewDidDisappear"),
                object: nil,
                userInfo: ["conversation_id": conversationId]
            )

            // 移除通知观察者
            NotificationCenter.default.removeObserver(self, name: NSNotification.Name("NewMessageReceived"), object: nil)
            NotificationCenter.default.removeObserver(self, name: NSNotification.Name("RefreshChatAfterBackground"), object: nil)
            NotificationCenter.default.removeObserver(self, name: NSNotification.Name("ConversationCacheCleared"), object: nil)
        }
    }

    // 更新文本框高度
    private func updateTextHeight() {
        let lineHeight: CGFloat = 20
        let padding: CGFloat = 16
        let lines = max(1, min(3, viewModel.inputText.components(separatedBy: .newlines).count))
        textHeight = CGFloat(lines) * lineHeight + padding
    }

    // 使用UIKit手动弹出用户个人资料页
    private func presentUserProfile() {
        let userProfileView = UserProfileView(
            profileUserId: otherUserId,
            profileUsername: otherUsername,
            profileAvatar: otherUserAvatar
        ).environmentObject(UserState.shared)

        let hostingController = UIHostingController(rootView: userProfileView)
        hostingController.modalPresentationStyle = .fullScreen

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            var topController = rootViewController
            while let presentedViewController = topController.presentedViewController {
                topController = presentedViewController
            }
            topController.present(hostingController, animated: true, completion: nil)
        }
    }
}

// 消息气泡视图
struct MessageBubble: View {
    let message: Message
    let otherUserAvatar: String
    let currentUserAvatar: String
    let onAvatarTap: () -> Void

    var body: some View {
        HStack(alignment: .bottom, spacing: 8) {
            if message.is_self {
                Spacer()

                // 自己的消息气泡
                Text(message.content)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .clipShape(BubbleShape(isFromCurrentUser: true))
                    .padding(.trailing, 8)

                // 自己的头像（右侧）
                Image(currentUserAvatar)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 40, height: 40)
                    .clipShape(Circle())
            } else {
                // 对方的头像（左侧）
                Button(action: onAvatarTap) {
                    if !otherUserAvatar.isEmpty {
                        if otherUserAvatar.contains("http") {
                            // 远程头像
                            AsyncImage(url: URL(string: otherUserAvatar)) { phase in
                                if let image = phase.image {
                                    image
                                        .resizable()
                                        .scaledToFill()
                                } else {
                                    Circle()
                                        .fill(Color.gray.opacity(0.3))
                                }
                            }
                            .frame(width: 40, height: 40)
                            .clipShape(Circle())
                        } else {
                            // 本地头像
                            Image(otherUserAvatar)
                                .resizable()
                                .scaledToFill()
                                .frame(width: 40, height: 40)
                                .clipShape(Circle())
                        }
                    } else {
                        // 默认头像
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 40, height: 40)
                    }
                }

                // 对方的消息气泡
                Text(message.content)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(Color(UIColor.systemGray6))
                    .foregroundColor(.black)
                    .clipShape(BubbleShape(isFromCurrentUser: false))
                    .padding(.leading, 8)

                Spacer()
            }
        }
        .padding(.horizontal, 4)
    }
}

// 气泡形状
struct BubbleShape: Shape {
    var isFromCurrentUser: Bool
    
    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, cornerRadius: 16)
        return Path(path.cgPath)
    }
}

// 聊天设置视图
struct ChatSettingsView: View {
    let conversationId: String
    let userId: Int
    let otherUserId: Int
    let otherUsername: String
    let otherUserAvatar: String
    let currentUserAvatar: String
    let onClearHistory: () -> Void
    @State private var showReportSheet = false
    @State private var showSearchView = false
    @State private var isMuted = false
    @State private var isLoading = false
    @State private var errorMessage: String?
    @Environment(\.dismiss) var dismiss

    var body: some View {
        NavigationView {
            List {
                Section {
                    // 查找聊天记录选项
                    Button(action: {
                        showSearchView = true
                    }) {
                        HStack {
                            Text("查找聊天记录")
                                .foregroundColor(.black)
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(.gray)
                                .font(.system(size: 14))
                        }
                    }

                    // 免打扰开关
                    HStack {
                        Text("消息免打扰")
                        Spacer()
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Toggle("", isOn: $isMuted)
                                .onChange(of: isMuted) { oldValue, newValue in
                                    setMuteStatus(newValue)
                                }
                        }
                    }
                }

                Section {
                    Button(action: {
                        showReportSheet = true
                    }) {
                        HStack {
                            Text("举报")
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }

                    Button(action: onClearHistory) {
                        HStack {
                            Text("清空聊天记录")
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }
                }

                if let errorMessage = errorMessage {
                    Section {
                        Text(errorMessage)
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("聊天设置")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showReportSheet) {
            ChatReportSheet(conversationId: conversationId, userId: userId)
        }
        .fullScreenCover(isPresented: $showSearchView) {
            ChatSearchView(
                conversationId: conversationId,
                currentUserId: userId,
                otherUserId: otherUserId,
                otherUsername: otherUsername,
                otherUserAvatar: otherUserAvatar,
                currentUserAvatar: currentUserAvatar
            )
        }
        .onAppear {
            loadMuteStatus()
        }
    }

    private func loadMuteStatus() {
        // 从本地管理器加载免打扰状态
        isMuted = ConversationMuteManager.shared.getMuteStatus(for: conversationId)
    }

    private func setMuteStatus(_ muted: Bool) {
        isLoading = true
        errorMessage = nil

        guard let url = URL(string: "https://sorealhuman.com:8888/messages/set_conversation_mute") else {
            errorMessage = "无效的URL"
            isLoading = false
            // 恢复开关状态
            isMuted = !muted
            return
        }

        let parameters: [String: Any] = [
            "user_id": userId,
            "conversation_id": conversationId,
            "is_muted": muted
        ]

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        } catch {
            errorMessage = "参数编码失败"
            isLoading = false
            // 恢复开关状态
            isMuted = !muted
            return
        }

        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                isLoading = false

                if let error = error {
                    errorMessage = "设置失败: \(error.localizedDescription)"
                    // 恢复开关状态
                    isMuted = !muted
                    return
                }

                guard let data = data else {
                    errorMessage = "没有数据"
                    // 恢复开关状态
                    isMuted = !muted
                    return
                }

                do {
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        if let success = json["success"] as? Bool, success {
                            // 设置成功，更新本地状态
                            ConversationMuteManager.shared.setMuteStatus(muted, for: conversationId)
                        } else {
                            // 设置失败
                            if let message = json["message"] as? String {
                                errorMessage = message
                            } else {
                                errorMessage = "设置免打扰状态失败"
                            }
                            // 恢复开关状态
                            isMuted = !muted
                        }
                    }
                } catch {
                    errorMessage = "解析响应失败"
                    // 恢复开关状态
                    isMuted = !muted
                }
            }
        }.resume()
    }
}

// 自定义聊天文本编辑器
struct ChatTextEditor: UIViewRepresentable {
    @Binding var text: String
    var onSubmit: () -> Void
    var onTextChange: () -> Void

    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.font = UIFont.systemFont(ofSize: 16)
        textView.backgroundColor = .clear
        textView.delegate = context.coordinator
        textView.isScrollEnabled = true
        textView.returnKeyType = .send
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 8, bottom: 8, right: 8)

        return textView
    }

    func updateUIView(_ uiView: UITextView, context: Context) {
        if uiView.text != text {
            uiView.text = text
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UITextViewDelegate {
        var parent: ChatTextEditor

        init(_ parent: ChatTextEditor) {
            self.parent = parent
        }

        func textViewDidChange(_ textView: UITextView) {
            parent.text = textView.text
            parent.onTextChange()
        }

        func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
            // 当用户点击键盘上的发送按钮时，text会是"\n"
            if text == "\n" {
                // 如果输入框不为空，则提交
                if !textView.text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    parent.onSubmit()
                }
                return false // 不插入换行符
            }
            return true
        }
    }
}

struct ChatView_Previews: PreviewProvider {
    static var previews: some View {
        ChatView(conversationId: "1_2", otherUserId: 2, otherUsername: "测试用户", userId: 1)
    }
}
