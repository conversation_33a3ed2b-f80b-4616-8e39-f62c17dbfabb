//
//  NIENIEApp.swift
//  NIENIE
//
//  Created by 刘德志 on 2025/6/12.
//

import SwiftUI
import UserNotifications

@main
struct NIENIEApp: App {
    // 创建用户状态并在全局环境中共享
    @StateObject var userState = UserState.shared
    @UIApplicationDelegateAdaptor private var appDelegate: AppDelegate
    
    var body: some Scene {
        WindowGroup {
            StartView()
                .environmentObject(userState)
        }
    }
}

// 应用程序代理，处理推送通知
class AppDelegate: NSObject, UIApplicationDelegate, UNUserNotificationCenterDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil) -> Bool {
        // 注册推送通知
        UNUserNotificationCenter.current().delegate = self
        APIService.shared.registerForPushNotifications()

        // 注册应用生命周期通知
        setupAppLifecycleNotifications()

        return true
    }

    // MARK: - 应用生命周期管理

    private func setupAppLifecycleNotifications() {
        // 监听应用进入后台
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )

        // 监听应用回到前台
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )

        // 监听应用变为活跃状态
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
    }

    @objc private func applicationDidEnterBackground() {
       
        // 记录进入后台的时间和当前页面状态
        UserState.shared.recordBackgroundState()
    }

    @objc private func applicationWillEnterForeground() {
       
        // 准备重连WebSocket
        UserState.shared.prepareForForeground()
    }

    @objc private func applicationDidBecomeActive() {
       
        // 立即重新连接WebSocket并处理可能的新消息
        UserState.shared.handleReturnToForeground()
    }
    
    // 获取设备令牌
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        let tokenParts = deviceToken.map { data in String(format: "%02.2hhx", data) }
        let token = tokenParts.joined()
       
        
        // 保存令牌到 UserDefaults
        UserDefaults.standard.set(token, forKey: "deviceToken")
        
        // 如果用户已登录，向服务器注册令牌
        if UserState.shared.isLoggedIn {
            UserState.shared.registerDeviceToken(token)
        }
    }
    
    // 注册失败处理
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("注册远程通知失败: \(error)")
    }
    
    // 处理远程推送
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable: Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        // 处理推送通知
        if let conversationId = userInfo["conversation_id"] as? String,
           let messageId = userInfo["message_id"] as? Int,
           let senderId = userInfo["sender_id"] as? Int {
            
            // 调用API处理消息
            APIService.shared.handlePushNotification(conversationId: conversationId, messageId: messageId)
            
            // 发送本地通知，通知UI更新
            NotificationCenter.default.post(
                name: NSNotification.Name("NewMessageReceived"),
                object: nil,
                userInfo: [
                    "conversation_id": conversationId,
                    "sender_id": senderId,
                    "message_id": messageId
                ]
            )
        }
        completionHandler(.newData)
    }
    
    // 前台接收通知
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        let userInfo = notification.request.content.userInfo

        // 检查当前用户所在页面
        let currentPage = UserState.shared.currentPage
        let currentConversationId = UserState.shared.currentConversationId

        if let conversationId = userInfo["conversation_id"] as? String {
            // 如果用户在当前会话的聊天页面，不显示通知，直接更新UI
            if currentPage == "ChatView" && currentConversationId == conversationId {
                // 发送本地通知更新UI，但不显示推送通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("NewMessageReceived"),
                    object: nil,
                    userInfo: userInfo
                )
                completionHandler([]) // 不显示通知
                return
            }

            // 如果用户在消息列表页面，不显示通知，直接更新列表
            if currentPage == "ConversationListView" {
                // 发送本地通知更新UI，但不显示推送通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("NewMessageReceived"),
                    object: nil,
                    userInfo: userInfo
                )
                completionHandler([]) // 不显示通知
                return
            }
        }

        // 其他情况正常显示通知
        completionHandler([.banner, .sound, .badge])
    }
    
    // 点击通知
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        
        // 处理通知点击事件
        if let conversationId = userInfo["conversation_id"] as? String,
           let senderId = userInfo["sender_id"] as? Int,
           let messageId = userInfo["message_id"] as? Int {
            
            // 发送通知，通知应用程序打开聊天界面
            NotificationCenter.default.post(name: NSNotification.Name("OpenConversation"), object: nil, userInfo: [
                "conversation_id": conversationId,
                "sender_id": senderId
            ])
            
            // 发送本地通知，通知UI更新
            NotificationCenter.default.post(
                name: NSNotification.Name("NewMessageReceived"),
                object: nil,
                userInfo: [
                    "conversation_id": conversationId,
                    "sender_id": senderId,
                    "message_id": messageId
                ]
            )
        }
        
        completionHandler()
    }
}
