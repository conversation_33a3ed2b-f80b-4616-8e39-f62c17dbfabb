import Foundation

// 分页消息缓存管理器
class MessageCacheManager {
    static let shared = MessageCacheManager()
    private init() {}

    private let pageSize = 30 // 每页消息数量

    // MARK: - 缓存文件路径
    private func getCacheFileURL(for conversationId: String) -> URL? {
        do {
            let documentsDirectory = try FileManager.default.url(
                for: .documentDirectory,
                in: .userDomainMask,
                appropriateFor: nil,
                create: true
            )
            return documentsDirectory.appendingPathComponent("messages_\(conversationId).json")
        } catch {
            print("获取缓存文件路径失败: \(error)")
            return nil
        }
    }

    private func getIndexFileURL(for conversationId: String) -> URL? {
        do {
            let documentsDirectory = try FileManager.default.url(
                for: .documentDirectory,
                in: .userDomainMask,
                appropriateFor: nil,
                create: true
            )
            return documentsDirectory.appendingPathComponent("messages_index_\(conversationId).json")
        } catch {
            print("获取索引文件路径失败: \(error)")
            return nil
        }
    }

    // MARK: - 保存消息到缓存（完整替换，仅用于初始化）
    func saveMessages(_ messages: [Message], for conversationId: String) {
        guard let fileURL = getCacheFileURL(for: conversationId) else { return }

        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(messages)
            try data.write(to: fileURL)
            print("保存 \(messages.count) 条消息到缓存")
        } catch {
            print("保存消息到缓存失败: \(error)")
        }
    }

    // MARK: - 消息索引结构
    private struct MessageIndex: Codable {
        var messageIds: Set<Int>
        var totalCount: Int
        var lastUpdated: Date
    }

    // MARK: - 增量添加新消息到缓存（真正的增量保存）
    func addMessages(_ newMessages: [Message], for conversationId: String) {
        guard !newMessages.isEmpty else { return }

        // 使用索引文件来快速检查重复，避免读取全部消息
        addMessagesWithIndex(newMessages, for: conversationId)
    }

    // MARK: - 使用索引的增量添加
    private func addMessagesWithIndex(_ newMessages: [Message], for conversationId: String) {
        guard let fileURL = getCacheFileURL(for: conversationId),
              let indexURL = getIndexFileURL(for: conversationId) else { return }

        // 读取或创建索引
        var messageIndex = loadMessageIndex(for: conversationId)

        // 过滤重复消息（使用索引快速检查）
        let uniqueNewMessages = newMessages.filter { !messageIndex.messageIds.contains($0.message_id) }

        if uniqueNewMessages.isEmpty {
            print("没有新消息需要添加")
            return
        }

        do {
            // 如果缓存文件不存在，直接保存
            if !FileManager.default.fileExists(atPath: fileURL.path) {
                saveMessages(uniqueNewMessages, for: conversationId)

                // 更新索引
                messageIndex.messageIds = Set(uniqueNewMessages.map { $0.message_id })
                messageIndex.totalCount = uniqueNewMessages.count
                messageIndex.lastUpdated = Date()
                saveMessageIndex(messageIndex, for: conversationId)

                print("创建新缓存，保存 \(uniqueNewMessages.count) 条消息")
                return
            }

            // 读取现有消息
            let existingData = try Data(contentsOf: fileURL)
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            var existingMessages = try decoder.decode([Message].self, from: existingData)

            // 追加新消息
            existingMessages.append(contentsOf: uniqueNewMessages)
            existingMessages.sort { $0.created_at < $1.created_at }

            // 保存更新后的消息
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(existingMessages)
            try data.write(to: fileURL)

            // 更新索引
            for message in uniqueNewMessages {
                messageIndex.messageIds.insert(message.message_id)
            }
            messageIndex.totalCount = existingMessages.count
            messageIndex.lastUpdated = Date()
            saveMessageIndex(messageIndex, for: conversationId)

            print("增量添加了 \(uniqueNewMessages.count) 条新消息，总数: \(existingMessages.count)")

        } catch {
            print("增量添加消息失败: \(error)")
        }
    }

    // MARK: - 索引管理
    private func loadMessageIndex(for conversationId: String) -> MessageIndex {
        guard let indexURL = getIndexFileURL(for: conversationId),
              FileManager.default.fileExists(atPath: indexURL.path) else {
            return MessageIndex(messageIds: Set(), totalCount: 0, lastUpdated: Date())
        }

        do {
            let data = try Data(contentsOf: indexURL)
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            return try decoder.decode(MessageIndex.self, from: data)
        } catch {
            print("读取消息索引失败: \(error)")
            return MessageIndex(messageIds: Set(), totalCount: 0, lastUpdated: Date())
        }
    }

    private func saveMessageIndex(_ index: MessageIndex, for conversationId: String) {
        guard let indexURL = getIndexFileURL(for: conversationId) else { return }

        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(index)
            try data.write(to: indexURL)
        } catch {
            print("保存消息索引失败: \(error)")
        }
    }
    
    // MARK: - 加载所有消息（仅用于内部操作，不暴露给外部）
    private func loadAllMessages(for conversationId: String) -> [Message] {
        guard let fileURL = getCacheFileURL(for: conversationId),
              FileManager.default.fileExists(atPath: fileURL.path) else {
            return []
        }
        
        do {
            let data = try Data(contentsOf: fileURL)
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            return try decoder.decode([Message].self, from: data)
        } catch {
            print("读取缓存消息失败: \(error)")
            return []
        }
    }
    
    // MARK: - 分页加载最新消息
    func loadLatestMessages(for conversationId: String, pageSize: Int = 30) -> [Message] {
        let allMessages = loadAllMessages(for: conversationId)
        let sortedMessages = allMessages.sorted { $0.created_at < $1.created_at }
        
        // 返回最新的 pageSize 条消息
        return Array(sortedMessages.suffix(pageSize))
    }
    
    // MARK: - 分页加载历史消息
    func loadHistoryMessages(
        for conversationId: String,
        beforeMessage: Message,
        pageSize: Int = 30
    ) -> [Message] {
        let allMessages = loadAllMessages(for: conversationId)
        let sortedMessages = allMessages.sorted { $0.created_at < $1.created_at }
        
        // 找到指定消息的位置
        guard let targetIndex = sortedMessages.firstIndex(where: { $0.message_id == beforeMessage.message_id }) else {
            return []
        }
        
        // 计算要加载的历史消息范围
        let endIndex = targetIndex
        let startIndex = max(0, endIndex - pageSize)
        
        if startIndex < endIndex {
            return Array(sortedMessages[startIndex..<endIndex])
        }
        
        return []
    }
    
    // MARK: - 检查是否还有更多历史消息
    func hasMoreHistory(for conversationId: String, beforeMessage: Message) -> Bool {
        let allMessages = loadAllMessages(for: conversationId)
        let sortedMessages = allMessages.sorted { $0.created_at < $1.created_at }
        
        guard let targetIndex = sortedMessages.firstIndex(where: { $0.message_id == beforeMessage.message_id }) else {
            return false
        }
        
        return targetIndex > 0
    }
    
    // MARK: - 获取消息总数（使用索引优化）
    func getMessageCount(for conversationId: String) -> Int {
        let index = loadMessageIndex(for: conversationId)
        return index.totalCount
    }
    
    // MARK: - 清空缓存
    func clearCache(for conversationId: String) {
        guard let fileURL = getCacheFileURL(for: conversationId),
              let indexURL = getIndexFileURL(for: conversationId) else { return }

        do {
            // 删除消息缓存文件
            if FileManager.default.fileExists(atPath: fileURL.path) {
                try FileManager.default.removeItem(at: fileURL)
            }

            // 删除索引文件
            if FileManager.default.fileExists(atPath: indexURL.path) {
                try FileManager.default.removeItem(at: indexURL)
            }

            print("清空了会话 \(conversationId) 的缓存和索引")
        } catch {
            print("清空缓存失败: \(error)")
        }
    }
    
    // MARK: - 删除指定消息
    func deleteMessage(_ messageId: Int, for conversationId: String) {
        var allMessages = loadAllMessages(for: conversationId)
        let originalCount = allMessages.count
        allMessages.removeAll { $0.message_id == messageId }

        if allMessages.count < originalCount {
            // 保存更新后的消息
            saveMessages(allMessages, for: conversationId)

            // 更新索引
            var index = loadMessageIndex(for: conversationId)
            index.messageIds.remove(messageId)
            index.totalCount = allMessages.count
            index.lastUpdated = Date()
            saveMessageIndex(index, for: conversationId)

            print("删除了消息 \(messageId)，剩余 \(allMessages.count) 条消息")
        }
    }
}
