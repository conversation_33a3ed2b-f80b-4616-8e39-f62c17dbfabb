import Foundation
import SwiftUI
import CoreGraphics

// 创作记录模型
struct CreationRecord: Identifiable, Codable {
    let id: UUID
    let fileName: String
    let creationDate: Date
    
    init(fileName: String) {
        self.id = UUID()
        self.fileName = fileName
        self.creationDate = Date()
    }
}

class UserState: ObservableObject {
    // 单例
    static let shared = UserState()

    // 用户信息
    @Published var isLoggedIn: Bool = false {
        didSet {
            // 登录状态改变时，注册设备令牌和WebSocket连接
            if isLoggedIn {
                registerSavedDeviceToken()
                connectWebSocket()
            } else {
                disconnectWebSocket()
            }
        }
    }
    @Published var userId: Int = 1  // 设置为1，与token匹配
    @Published var username: String = "ldz"
    @Published var avatar: String = "4-2"
    @Published var token: String = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwianRpIjoiYWYxYjFhZTgtYzE4YS00ZGVhLTk2NzMtNjViNzIyNzA0ZmFmIiwiZXhwIjoxNzUzNDM3NDAyfQ.3EFc6ffE1TJmKVvv0mwbzR4qsIbLw_EksEMF7Sh6bxA"
    @Published var balance: Int = 100 {
        didSet {
            // 确保存储操作在主线程
            DispatchQueue.main.async {
                UserDefaults.standard.set(self.balance, forKey: self.balanceKey)
            }
        }
    }

    // 当前页面状态，用于智能通知显示
    @Published var currentPage: String = ""
    @Published var currentConversationId: String = ""

    // WebSocket连接状态
    @Published var isWebSocketConnected: Bool = false

    // 设备token注册标记
    @Published var hasRegisteredDeviceToken: Bool = false
    
    // 不感兴趣内容存储
    @Published var dislikedImageIds: Set<Int> = [] {
        didSet {
            saveDislikedContent()
        }
    }
    
    // 创作记录相关
    @Published var creationRecords: [CreationRecord] = [] {
        didSet {
            saveCreationRecords()
        }
    }
    
    // 审核状态相关
    @Published var pendingAuditImages: [[String: Any]] = []
    @Published var hasAuditStatusChanged: Bool = false
    private var auditCheckTimer: Timer?
    
    // AI创作弹窗状态
    @Published var showAICreateSheet: Bool = false
    
    // 添加隐私政策显示状态
    @Published var showPrivacyPolicy: Bool = false
    
    // 当前选中的标签页
    @Published var selectedTab: Int = 0
    
    // 悬浮球相关状态
    @Published var showFloatingBall: Bool = false
    @Published var isCreationInProgress: Bool = false
    @Published var isCreationComplete: Bool = false
    @Published var floatingBallPosition: CGPoint = CGPoint(x: UIScreen.main.bounds.width - 50, y: UIScreen.main.bounds.height / 2)
    @Published var taskId: String? = nil
    @Published var processingStatus: String = "processing" // processing, completed, failed
    
    // 保存创作页面状态的属性
    @Published var outputImage: UIImage? = nil
    @Published var originalOutputImage: UIImage? = nil // 存储原始输出图像，用于补光
    @Published var outputImagePath: String = ""
    @Published var bgImagePath: String = ""
    @Published var templateBackgroundPath: String = "" // 添加用于存储模版背景图路径
    @Published var isCreatingFromTemplate: Bool = false // 标记是否从详情页模版创作
    @Published var selectedCharacterImage: UIImage? = nil // 保存用户选择的人物图像
    @Published var navigateToImageAdjustFromTemplate: Bool = false // 控制模版创作的导航逻辑
    @Published var isImageEnhanced: Bool = false // 标记图片是否被人物补光增强过
    
    // 持久化键
    private let tokenKey = "userToken"
    private let userIdKey = "userId"
    private let usernameKey = "username"
    private let avatarKey = "avatar"
    private let balanceKey = "balance" // 添加余额键
    private let creationRecordsKey = "creationRecords" // 创作记录键
    private let dislikedImageIdsKey = "dislikedImageIds" // 不感兴趣的图片ID
    private let deviceTokenKey = "deviceToken"
    private let hasRegisteredDeviceTokenKey = "hasRegisteredDeviceToken" // 设备令牌键
    
    init() {
        // 从UserDefaults加载保存的用户信息
        loadUserData()

        // 如果token为空，设置为默认token
        if token.isEmpty {
            token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwianRpIjoiODU0ZTNjYmYtMDYyZS00YjYwLTkxYWYtZjgwNjFkZDRjNzg3IiwiZXhwIjoxNzU0NDYyMjUzfQ.xaXdz4F49lqvWAl_HrZVEtGSrzLHchG5UfYfrRVysQw"
            userId = 1
            isLoggedIn = true
            saveUserData()
        }

        // 加载创作记录
        loadCreationRecords()
        // 加载不感兴趣内容
        loadDislikedContent()

        // 初始化全局会话过滤管理器
        _ = ConversationFilterManager.shared

        // 如果用户已登录，注册设备令牌
        if isLoggedIn {
            registerSavedDeviceToken()
        }
    }
    
    // 注册保存的设备令牌
    private func registerSavedDeviceToken() {
        guard isLoggedIn,
              !hasRegisteredDeviceToken,
              let deviceToken = UserDefaults.standard.string(forKey: deviceTokenKey) else { return }

        registerDeviceToken(deviceToken)
    }
    
    // 向服务器注册设备令牌
    func registerDeviceToken(_ token: String) {
        let url = URL(string: "https://sorealhuman.com:8888/messages/register_device_token")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let parameters: [String: Any] = [
            "user_id": userId,
            "device_token": token
        ]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        } catch {
            print("注册设备令牌失败: 参数编码错误")
            return
        }
        
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            if let error = error {
                print("注册设备令牌失败: \(error.localizedDescription)")
                return
            }

            guard data != nil else {
                print("注册设备令牌失败: 没有数据返回")
                return
            }

            print("设备令牌注册成功")

            // 标记已注册设备令牌
            DispatchQueue.main.async {
                self?.hasRegisteredDeviceToken = true
                UserDefaults.standard.set(true, forKey: self?.hasRegisteredDeviceTokenKey ?? "")
            }
        }.resume()
    }

    // 清空设备令牌
    func clearDeviceTokens() {
        let url = URL(string: "https://sorealhuman.com:8888/messages/clear_device_tokens")!
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        let parameters: [String: Any] = [
            "user_id": userId
        ]

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        } catch {
            print("清空设备令牌失败: 参数编码错误")
            return
        }

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("清空设备令牌失败: \(error.localizedDescription)")
                return
            }

            print("设备令牌清空成功")
        }.resume()
    }

    // MARK: - WebSocket管理

    func connectWebSocket() {
        guard isLoggedIn && userId > 0 else { return }

        WebSocketManager.shared.connect(userId: userId)

        // 监听WebSocket连接状态
        DispatchQueue.main.async {
            self.isWebSocketConnected = WebSocketManager.shared.isConnected
        }

        print("WebSocket连接已启动")
    }

    func disconnectWebSocket() {
        WebSocketManager.shared.disconnect()

        DispatchQueue.main.async {
            self.isWebSocketConnected = false
        }

        print("WebSocket连接已断开")
    }

    func updatePageStatus(page: String, conversationId: String = "") {
        currentPage = page
        currentConversationId = conversationId

        // 通过WebSocket更新页面状态
        WebSocketManager.shared.updatePageStatus(page: page, conversationId: conversationId)
    }

    // MARK: - 应用生命周期管理

    private var backgroundTime: Date?
    private var wasInChatView: Bool = false
    private var backgroundConversationId: String = ""

    func recordBackgroundState() {
        backgroundTime = Date()
        wasInChatView = (currentPage == "ChatView")
        backgroundConversationId = currentConversationId

    }

    func prepareForForeground() {
    }

    func handleReturnToForeground() {

        // 如果用户已登录，立即重新连接WebSocket
        if isLoggedIn {
            // 使用强制重连确保连接建立
            WebSocketManager.shared.forceReconnect()

            // 更新连接状态
            DispatchQueue.main.async {
                self.isWebSocketConnected = WebSocketManager.shared.isConnected
            }

            // 延迟一点时间确保连接建立后再更新页面状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                // 恢复页面状态
                if self.wasInChatView && !self.backgroundConversationId.isEmpty {
                    self.updatePageStatus(page: "ChatView", conversationId: self.backgroundConversationId)

                    // 通知聊天页面刷新消息
                    NotificationCenter.default.post(
                        name: NSNotification.Name("RefreshChatAfterBackground"),
                        object: nil,
                        userInfo: ["conversation_id": self.backgroundConversationId]
                    )
                } else {
                    self.updatePageStatus(page: self.currentPage, conversationId: self.currentConversationId)
                }

                // 通知会话列表刷新
                NotificationCenter.default.post(
                    name: NSNotification.Name("RefreshConversationListAfterBackground"),
                    object: nil,
                    userInfo: nil
                )
            }
        }

        // 重置后台状态
        backgroundTime = nil
        wasInChatView = false
        backgroundConversationId = ""
    }
    
    // 检查审核中的图片状态 - 只在必要时调用，避免重复请求
    func checkPendingAuditStatus() {
        guard isLoggedIn else { return }
        
        // 只有在初始登录或明确需要刷新时才请求
        if pendingAuditImages.isEmpty {
            APIService.shared.getPendingAuditImages(userId: userId) { [weak self] result in
                DispatchQueue.main.async {
                    guard let self = self else { return }
                    
                    switch result {
                    case .success(let response):
                        if let success = response["success"] as? Bool, success,
                           let pendingImages = response["pending_images"] as? [[String: Any]] {
                            
                            // 更新审核中的图片
                            self.pendingAuditImages = pendingImages
                            
                            // 如果有审核中的图片，标记状态变化
                            if !pendingImages.isEmpty {
                                self.hasAuditStatusChanged = true
                                
                                // 发送通知，更新用户图片列表
                                NotificationCenter.default.post(name: NSNotification.Name("RefreshUserImages"), object: nil)
                            }
                        }
                    case .failure(let error):
                        print("检查审核状态失败: \(error.localizedDescription)")
                    }
                }
            }
        }
    }
    
    // 保存用户数据
    func saveUserData() {
        UserDefaults.standard.set(token, forKey: tokenKey)
        UserDefaults.standard.set(userId, forKey: userIdKey)
        UserDefaults.standard.set(username, forKey: usernameKey)
        UserDefaults.standard.set(avatar, forKey: avatarKey)
        UserDefaults.standard.set(balance, forKey: balanceKey) // 保存余额
    }
    
    // 加载用户数据
    func loadUserData() {
        token = UserDefaults.standard.string(forKey: tokenKey) ?? ""
        userId = UserDefaults.standard.integer(forKey: userIdKey)
        username = UserDefaults.standard.string(forKey: usernameKey) ?? ""
        avatar = UserDefaults.standard.string(forKey: avatarKey) ?? ""
        balance = UserDefaults.standard.integer(forKey: balanceKey) // 加载余额
        hasRegisteredDeviceToken = UserDefaults.standard.bool(forKey: hasRegisteredDeviceTokenKey) // 加载设备token注册状态

        // 如果有token，则设置登录状态
        isLoggedIn = !token.isEmpty
    }
    
    // 更新余额
    func updateBalance(newBalance: Int) {
        self.balance = newBalance
        UserDefaults.standard.set(balance, forKey: balanceKey)
    }
    
    // 登录
    func login(userId: Int, username: String, avatar: String, token: String, balance: Int? = nil) {
        self.userId = userId
        self.username = username
        self.avatar = avatar
        self.token = token

        // 如果提供了余额，直接更新
        if let newBalance = balance {
            self.balance = newBalance
        }

        // 重置设备token注册标记，确保新登录时会重新注册
        self.hasRegisteredDeviceToken = false
        UserDefaults.standard.set(false, forKey: hasRegisteredDeviceTokenKey)

        // 保存到UserDefaults
        saveUserData()

        // 设置登录状态（会触发didSet，自动注册设备令牌）
        self.isLoggedIn = true

        // 启动审核状态检查定时器
        checkPendingAuditStatus()
    }
    
    // 从服务器获取余额
    func fetchBalanceFromServer() {
        APIService.shared.getUserBalance(userId: userId) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let balance):
                    
                    self.updateBalance(newBalance: balance)
                case .failure(let error):
                    print("获取余额失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // 登出
    func logout() {
        // 先清空服务器端的设备令牌
        clearDeviceTokens()

        userId = 0
        username = ""
        avatar = ""
        token = ""
        balance = 0 // 清除余额

        // 清空审核状态相关数据
        pendingAuditImages = []
        hasAuditStatusChanged = false

        // 清空创作记录
        clearAllCreationRecords()

        // 清除不感兴趣内容
        clearDislikedContent()

        // 清除免打扰状态
        ConversationMuteManager.shared.clearMuteStates(for: userId)

        // 清除设备token注册标记
        hasRegisteredDeviceToken = false

        // 清除UserDefaults中的用户数据
        UserDefaults.standard.removeObject(forKey: tokenKey)
        UserDefaults.standard.removeObject(forKey: userIdKey)
        UserDefaults.standard.removeObject(forKey: usernameKey)
        UserDefaults.standard.removeObject(forKey: avatarKey)
        UserDefaults.standard.removeObject(forKey: balanceKey) // 清除余额
        UserDefaults.standard.removeObject(forKey: creationRecordsKey) // 清除创作记录
        UserDefaults.standard.removeObject(forKey: dislikedImageIdsKey) // 清除不感兴趣图片
        UserDefaults.standard.removeObject(forKey: hasRegisteredDeviceTokenKey) // 清除设备token注册标记

        // 注意：不要删除设备令牌，因为它需要被保留以供未来登录使用
        
        // 设置登录状态为false
        isLoggedIn = false
    }
    
    // 注销账户
    func deleteAccount(completion: @escaping (Result<Bool, Error>) -> Void) {
        guard isLoggedIn else {
            completion(.failure(NSError(domain: "UserState", code: 0, userInfo: [NSLocalizedDescriptionKey: "用户未登录"])))
            return
        }
        
        APIService.shared.deleteAccount(userId: userId) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let success):
                    if success {
                        // 注销成功后执行登出操作
                        self.logout()
                    }
                    completion(.success(success))
                case .failure(let error):
                    completion(.failure(error))
                }
            }
        }
    }
    
    // 清空所有创作记录及其文件
    private func clearAllCreationRecords() {
        // 删除所有创作记录文件
        for record in creationRecords {
            deleteCreationFile(fileName: record.fileName)
        }
        
        // 清空记录数组
        creationRecords = []
    }
    
    func resetCreationState() {
        showFloatingBall = false
        isCreationInProgress = false
        isCreationComplete = false
        taskId = nil
        processingStatus = "processing"
        outputImage = nil
        originalOutputImage = nil // 重置原始图像
        outputImagePath = ""
        bgImagePath = ""
        templateBackgroundPath = "" // 重置模版背景图路径
        isCreatingFromTemplate = false // 重置创作来源标记
        selectedCharacterImage = nil
        navigateToImageAdjustFromTemplate = false
        isImageEnhanced = false // 重置图片增强状态
    }
    
    // 重置仅模版创作相关的状态，保留其他创作状态
    func resetTemplateCreationState() {
        templateBackgroundPath = ""
        isCreatingFromTemplate = false
        selectedCharacterImage = nil
        navigateToImageAdjustFromTemplate = false
    }
    
    // 保存创作记录到UserDefaults
    private func saveCreationRecords() {
        if let encoded = try? JSONEncoder().encode(creationRecords) {
            UserDefaults.standard.set(encoded, forKey: creationRecordsKey)
        }
    }
    
    // 检查是否有正在进行的任务
    func hasActiveCreationTask() -> Bool {
        return showFloatingBall && isCreationInProgress
    }
    
    // 从UserDefaults加载创作记录
    private func loadCreationRecords() {
        if let savedRecords = UserDefaults.standard.data(forKey: creationRecordsKey) {
            if let decodedRecords = try? JSONDecoder().decode([CreationRecord].self, from: savedRecords) {
                creationRecords = decodedRecords
            }
        }
    }
    
    // 添加创作记录
    func addCreationRecord(fileName: String) {
        let record = CreationRecord(fileName: fileName)
        creationRecords.append(record)
        self.balance -= 2 // 生成作品消耗余额
    }
    
    // 根据ID删除创作记录
    func deleteCreationRecord(id: UUID) {
        // 找到记录的索引
        if let index = creationRecords.firstIndex(where: { $0.id == id }) {
            // 获取文件名
            let fileName = creationRecords[index].fileName
            
            // 删除文件
            deleteCreationFile(fileName: fileName)
            
            // 从记录列表中移除
            creationRecords.remove(at: index)
        }
    }
    
    // 获取创作记录对应的文件URL
    public func getCreationFileURL(fileName: String) -> URL? {
        let fileManager = FileManager.default
        do {
            let documentsURL = try fileManager.url(
                for: .documentDirectory,
                in: .userDomainMask,
                appropriateFor: nil,
                create: false
            )
            
            let creationsURL = documentsURL.appendingPathComponent("CreationRecords")
            
            // 创建文件夹（如果不存在）
            if !fileManager.fileExists(atPath: creationsURL.path) {
                try fileManager.createDirectory(at: creationsURL, withIntermediateDirectories: true)
            }
            
            return creationsURL.appendingPathComponent(fileName)
        } catch {
            return nil
        }
    }
    
    // 保存创作图片到本地文件
    func saveCreationImage(image: UIImage) -> String? {
        // 使用任务ID作为文件名，如果没有则使用UUID
        let fileName = "creation_\(UUID().uuidString).jpg"
        // let fileName = taskId != nil ? "\(taskId!).jpg" : "creation_\(UUID().uuidString).jpg"
        
        guard let fileURL = getCreationFileURL(fileName: fileName) else {
            return nil
        }
        
        guard let imageData = image.jpegData(compressionQuality: 1.0) else {
            return nil
        }
        
        
        do {
            try imageData.write(to: fileURL)
            
            
            // 添加记录
            addCreationRecord(fileName: fileName)
            
            return fileName
        } catch {
            return nil
        }
    }
    
    // 加载创作图片
    func loadCreationImage(fileName: String) -> UIImage? {
        guard let fileURL = getCreationFileURL(fileName: fileName) else {
            return nil
        }
        
        do {
            let imageData = try Data(contentsOf: fileURL)
            return UIImage(data: imageData)
        } catch {
            return nil
        }
    }
    
    // 加载创作图片的缩略图（降采样处理）
    func loadCreationImageThumbnail(fileName: String, size: CGSize) -> UIImage? {
        guard let fileURL = getCreationFileURL(fileName: fileName) else {
            return nil
        }
        
        // 使用降采样技术加载图片，减少内存占用
        let imageSourceOptions = [kCGImageSourceShouldCache: false] as CFDictionary
        guard let imageSource = CGImageSourceCreateWithURL(fileURL as CFURL, imageSourceOptions) else {
            return nil
        }
        
        let maxDimensionInPixels = max(size.width, size.height)
        let downsampleOptions = [
            kCGImageSourceCreateThumbnailFromImageAlways: true,
            kCGImageSourceShouldCacheImmediately: true,
            kCGImageSourceCreateThumbnailWithTransform: true,
            kCGImageSourceThumbnailMaxPixelSize: maxDimensionInPixels
        ] as CFDictionary
        
        guard let downsampledImage = CGImageSourceCreateThumbnailAtIndex(imageSource, 0, downsampleOptions) else {
            return nil
        }
        
        return UIImage(cgImage: downsampledImage)
    }
    
    // 删除创作文件
    private func deleteCreationFile(fileName: String) {
        guard let fileURL = getCreationFileURL(fileName: fileName) else { return }
        
        do {
            try FileManager.default.removeItem(at: fileURL)
        } catch {
            print("删除文件失败: \(error)")
        }
    }
    
    // 检查图像是否已经存在于创作记录中
    func checkIfImageExists(image: UIImage) -> Bool {
        // 如果有任务ID，检查是否存在相同文件名的记录
        if let taskId = self.taskId {
            let fileName = "\(taskId).jpg"
            return creationRecords.contains { $0.fileName == fileName }
        }
        
        // 如果没有任务ID，则返回false
        return false
    }
    
    // 更新创作记录中的图像
    func updateCreationImage(originalImage: UIImage, newImage: UIImage) -> String? {
        // 查找与原始图像关联的记录
        guard let taskId = self.taskId else { return nil }
        let fileName = "\(taskId).jpg"
        
        guard let recordIndex = creationRecords.firstIndex(where: { $0.fileName == fileName }) else {
            return nil
        }
        
        // 删除旧文件
        deleteCreationFile(fileName: fileName)
        
        // 保存新文件
        let newFileName = "creation_\(UUID().uuidString).jpg"
        guard let fileURL = getCreationFileURL(fileName: newFileName) else {
            return nil
        }
        
        guard let imageData = newImage.jpegData(compressionQuality: 1.0) else {
            return nil
        }
        
        do {
            try imageData.write(to: fileURL)
            
            // 更新记录中的文件名
            creationRecords[recordIndex] = CreationRecord(fileName: newFileName)
            
            return newFileName
        } catch {
            return nil
        }
    }
    
    // 保存不感兴趣内容
    func saveDislikedContent() {
        if let imageIdsData = try? JSONEncoder().encode(Array(dislikedImageIds)) {
            UserDefaults.standard.set(imageIdsData, forKey: dislikedImageIdsKey)
        }
    }
    
    // 加载不感兴趣内容
    func loadDislikedContent() {
        if let imageIdsData = UserDefaults.standard.data(forKey: dislikedImageIdsKey),
           let imageIds = try? JSONDecoder().decode([Int].self, from: imageIdsData) {
            dislikedImageIds = Set(imageIds)
        }
    }
    
    // 清除不感兴趣内容
    func clearDislikedContent() {
        dislikedImageIds.removeAll()
        saveDislikedContent()
    }
    
    // 添加不感兴趣的图片
    func addDislikedImage(imageId: Int) {
        dislikedImageIds.insert(imageId)
    }
    
    // 检查图片是否被标记为不感兴趣
    func isImageDisliked(imageId: Int) -> Bool {
        return dislikedImageIds.contains(imageId)
    }
    
    // 检查内容是否应该被过滤（图片ID在不感兴趣列表中）
    func shouldFilterContent(imageId: Int, username: String) -> Bool {
        return dislikedImageIds.contains(imageId)
    }
} 
